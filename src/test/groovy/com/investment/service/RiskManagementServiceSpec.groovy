package com.investment.service

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.model.Position
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test specification for RiskManagementService enhanced risk management logic.
 */
class RiskManagementServiceSpec extends Specification {

    @Subject
    RiskManagementService riskManagementService

    DatabaseManager mockDatabaseManager = Mock()

    def setup() {
        riskManagementService = new RiskManagementService()
        riskManagementService.databaseManager = mockDatabaseManager
    }

    def "should use aggressive mode for positions in initial period (days 1-2)"() {
        given: "a position opened 1 day ago"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(1)
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning 1 trading day"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 1

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should use aggressive mode and calculate stop value accordingly"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05)
    }

    def "should use conservative mode when Bollinger Band distance is contracting"() {
        given: "a position opened 5 days ago"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(5)
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning 5 trading days (beyond initial period)"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 5

        and: "OHLCV data showing contracting Bollinger Band distance"
        def ohlcvData = [
            // T-1: distance = |92 - 90| = 2 (contracting - closer to BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(1), 91.0, 93.0, 90.0, 92.0, 1000000L, 90.0, null, null, null),
            // T-2: distance = |95 - 90| = 5 (was further from BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(2), 94.0, 96.0, 93.0, 95.0, 1000000L, 90.0, null, null, null),
            // T-3: for minimum data requirement
            new OHLCV("AAPL", LocalDate.now().minusDays(3), 88.0, 90.0, 87.0, 89.0, 1000000L, 85.0, null, null, null)
        ]

        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> ohlcvData

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should switch to conservative mode for 2 days"
        position.riskMode == Position.RiskMode.CONSERVATIVE
        position.conservativePeriodEndDate != null
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05) - now uses same stop_percentage
    }

    def "should continue aggressive mode when Bollinger Band distance is expanding"() {
        given: "a position opened 5 days ago"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(5)
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning 5 trading days (beyond initial period)"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 5

        and: "OHLCV data showing expanding Bollinger Band distance"
        def ohlcvData = [
            // T-1: distance = |98 - 90| = 8 (further from BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(1), 97.0, 99.0, 96.0, 98.0, 1000000L, 90.0, null, null, null),
            // T-2: distance = |95 - 90| = 5 (closer to BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(2), 94.0, 96.0, 93.0, 95.0, 1000000L, 90.0, null, null, null),
            // T-3: for minimum data requirement
            new OHLCV("AAPL", LocalDate.now().minusDays(3), 88.0, 90.0, 87.0, 89.0, 1000000L, 85.0, null, null, null)
        ]

        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> ohlcvData

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should continue with aggressive mode"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05)
    }

    def "should handle SELL positions with inverted logic"() {
        given: "a SELL position opened 5 days ago"
        def position = createTestPosition()
        position.side = Position.Side.SELL
        position.openDate = LocalDate.now().minusDays(5)
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00") // Lowest price for SELL positions

        and: "mock database manager returning 5 trading days (beyond initial period)"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 5

        and: "OHLCV data showing expanding distance (for SELL, use body top)"
        def ohlcvData = [
            // T-1: distance = |110 - 95| = 15 (body top further from BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(1), 108.0, 112.0, 107.0, 110.0, 1000000L, 95.0, null, null, null),
            // T-2: distance = |105 - 95| = 10 (body top closer to BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(2), 103.0, 107.0, 102.0, 105.0, 1000000L, 95.0, null, null, null),
            // T-3: for minimum data requirement
            new OHLCV("AAPL", LocalDate.now().minusDays(3), 100.0, 102.0, 99.0, 101.0, 1000000L, 98.0, null, null, null)
        ]

        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> ohlcvData

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should use aggressive mode and calculate stop value for SELL position"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("105.000000") // 100 * (1 + 0.05) for SELL positions
    }

    def "should remain in conservative mode during forced conservative period"() {
        given: "a position currently in conservative period"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(5)
        position.riskMode = Position.RiskMode.CONSERVATIVE
        position.conservativePeriodEndDate = LocalDateTime.now().plusDays(1) // Still in conservative period
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning 5 trading days (beyond initial period)"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 5

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should remain in conservative mode regardless of market conditions"
        position.riskMode == Position.RiskMode.CONSERVATIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05) - now uses same stop_percentage
    }

    def "should fallback to standard calculation when insufficient OHLCV data"() {
        given: "a position with insufficient historical data"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(5)
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning 5 trading days (beyond initial period)"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 5

        and: "insufficient OHLCV data for Bollinger Band analysis"
        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> []

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should default to aggressive mode"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05)
    }

    def "should initialize risk parameters correctly"() {
        given: "a new position"
        def position = createTestPosition()
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage

        when: "initializing risk parameters"
        riskManagementService.initializeRiskParameters(position)

        then: "should set risk mode correctly"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        position.stopPercent == new BigDecimal("0.05") // Stop percentage should remain unchanged
    }

    def "should calculate trading days age for OPEN position using OHLCV data"() {
        given: "an OPEN position with openDate 5 days ago"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(5)
        position.closeDate = null // OPEN position
        position.status = Position.Status.OPEN
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning 4 trading days"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 4

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should query OHLCV data without upper date bound for OPEN position"
        1 * mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null)
        // With 4 trading days (beyond initial period), should proceed to Bollinger Band analysis
        result != null
    }

    def "should calculate trading days age for CLOSED position using OHLCV data"() {
        given: "a CLOSED position with openDate and closeDate"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(10)
        position.closeDate = LocalDate.now().minusDays(3)
        position.status = Position.Status.CLOSED
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning 5 trading days between open and close dates"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, position.closeDate) >> 5

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should query OHLCV data with both openDate and closeDate bounds"
        1 * mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, position.closeDate)
        // With 5 trading days (beyond initial period), should proceed to Bollinger Band analysis
        result != null
    }

    def "should handle zero trading days when no OHLCV data found"() {
        given: "a position with no OHLCV data available"
        def position = createTestPosition()
        position.openDate = LocalDate.now().minusDays(5)
        position.closeDate = null
        position.stopPercent = new BigDecimal("0.05")  // 5% - using universal stop percentage
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "mock database manager returning zero count"
        mockDatabaseManager.countOhlcvRecordsInDateRange("AAPL", position.openDate, null) >> 0

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should handle zero trading days gracefully and use initial period logic"
        // With 0 trading days, position should be in initial period (aggressive mode)
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05)
    }

    private Position createTestPosition() {
        def position = new Position()
        position.id = 1L
        position.symbol = "AAPL"
        position.side = Position.Side.BUY
        position.status = Position.Status.OPEN
        position.tradePrice = new BigDecimal("95.00")
        position.position = new BigDecimal("100")
        position.createdDate = LocalDateTime.now()
        position.openDate = LocalDate.now().minusDays(1) // Default to 1 day ago
        return position
    }
}
